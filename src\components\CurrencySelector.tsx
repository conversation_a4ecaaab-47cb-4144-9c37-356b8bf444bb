'use client'

import { useState } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ChevronDown, Banknote } from 'lucide-react'
import { getCurrencyList, getCurrency, type Currency } from '@/lib/currency'

interface CurrencySelectorProps {
  selectedCurrency: string;
  onCurrencyChange: (currencyCode: string) => void;
  lang: 'en' | 'fr' | 'ar';
  dict: any;
}

export default function CurrencySelector({ 
  selectedCurrency, 
  onCurrencyChange, 
  lang,
  dict 
}: CurrencySelectorProps) {
  const currencies = getCurrencyList()
  const currentCurrency = getCurrency(selectedCurrency)

  // Group currencies by region for better organization
  const gccCurrencies = currencies.filter(c => 
    ['AED', 'SAR', 'KWD', 'QAR', 'BHD', 'OMR'].includes(c.code)
  )
  const maghrebCurrencies = currencies.filter(c => 
    ['MAD', 'TND', 'DZD', 'LYD'].includes(c.code)
  )
  const mashreqCurrencies = currencies.filter(c => 
    ['EGP', 'JOD', 'LBP', 'SYP'].includes(c.code)
  )
  const otherCurrencies = currencies.filter(c => 
    ['IQD', 'YER', 'SDG'].includes(c.code)
  )

  const getCountryFlag = (countryCode: string) => {
    // Convert country code to flag emoji
    const codePoints = countryCode
      .toUpperCase()
      .split('')
      .map(char => 127397 + char.charCodeAt(0))
    return String.fromCodePoint(...codePoints)
  }

  const CurrencyGroup = ({ 
    title, 
    currencies 
  }: { 
    title: string; 
    currencies: Currency[] 
  }) => (
    <>
      {currencies.length > 0 && (
        <>
          <div className="px-2 py-1.5 text-xs font-semibold text-muted-foreground border-b">
            {title}
          </div>
          {currencies.map((currency) => (
            <DropdownMenuItem
              key={currency.code}
              onClick={() => onCurrencyChange(currency.code)}
              className={`flex items-center gap-3 px-3 py-2 cursor-pointer ${
                selectedCurrency === currency.code ? 'bg-accent' : ''
              }`}
            >
              <span className="text-lg">
                {getCountryFlag(currency.countryCode)}
              </span>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-sm">
                    {currency.name[lang]}
                  </span>
                  <Badge variant="outline" className="text-xs">
                    {currency.code}
                  </Badge>
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {currency.country[lang]}
                </div>
              </div>
              <span className="text-sm font-mono text-muted-foreground">
                {currency.symbol}
              </span>
            </DropdownMenuItem>
          ))}
        </>
      )}
    </>
  )

  return (
    <div className="w-full">
      <label className="block text-sm font-medium mb-2">
        {dict.calculator?.currency || 'Currency'}
      </label>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="w-full justify-between h-12 px-4"
            dir={lang === 'ar' ? 'rtl' : 'ltr'}
          >
            <div className="flex items-center gap-3">
              <Banknote className="h-4 w-4 text-muted-foreground" />
              {currentCurrency && (
                <>
                  <span className="text-lg">
                    {getCountryFlag(currentCurrency.countryCode)}
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {currentCurrency.name[lang]}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      {currentCurrency.code}
                    </Badge>
                  </div>
                </>
              )}
            </div>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          className="w-80 max-h-96 overflow-y-auto"
          align={lang === 'ar' ? 'end' : 'start'}
        >
          <CurrencyGroup 
            title={dict.regions?.gcc || "Gulf Cooperation Council"}
            currencies={gccCurrencies}
          />
          <CurrencyGroup 
            title={dict.regions?.maghreb || "Maghreb"}
            currencies={maghrebCurrencies}
          />
          <CurrencyGroup 
            title={dict.regions?.mashreq || "Mashreq"}
            currencies={mashreqCurrencies}
          />
          <CurrencyGroup 
            title={dict.regions?.other || "Other Arab Countries"}
            currencies={otherCurrencies}
          />
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
