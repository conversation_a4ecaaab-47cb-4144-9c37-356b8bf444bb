// Currency formatting utilities

export const formatCurrency = (value: number, locale: string = 'en') => {
  // For Arabic locale, use Arabic-Indic numerals
  if (locale === 'ar') {
    return new Intl.NumberFormat('ar-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }
  
  // For French locale
  if (locale === 'fr') {
    return new Intl.NumberFormat('fr-MA', {
      style: 'currency',
      currency: 'MAD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }

  // Default English formatting
  return new Intl.NumberFormat('en-MA', {
    style: 'currency',
    currency: 'MAD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

export const formatNumber = (value: number, locale: string = 'en') => {
  // For Arabic locale, use Arabic-Indic numerals
  if (locale === 'ar') {
    return new Intl.NumberFormat('ar-MA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }

  // For French locale
  if (locale === 'fr') {
    return new Intl.NumberFormat('fr-MA', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(value);
  }

  // Default English formatting
  return new Intl.NumberFormat('en-MA', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  }).format(value);
};


